on:
  push: 
    branches: [main]

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
        DATABASE_URL: ${{ secrets.DATABASE_URL }}
  
    steps:

      - name: Check Out code
        uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: 18
      
      - name: Install dependencies
        run: npm ci

      - name: Build the app
        run: npm run build

      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
            credentials_json: ${{ secrets.GCP_CREDENTIALS}}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v3
        with:
            version: '>= 363.0.0'

      - name: Build and push image to Artifact Registry
        run: |
          gcloud builds submit --tag us-central1-docker.pkg.dev/notely-471423/notely-ar-repo/notely:${{ github.sha }} .

      - name: Migrate DATABASE
        run: npm run db:migrate
      - name: Deploy to Cloud Run
        run: gcloud run deploy notely --image us-central1-docker.pkg.dev/notely-471423/notely-ar-repo/notely:${{ github.sha }} --region us-central1 --allow-unauthenticated --project notely-471423 --max-instances=4